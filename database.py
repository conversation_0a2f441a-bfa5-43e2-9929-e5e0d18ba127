import sqlite3
from datetime import datetime

class Database:
    def __init__(self, db_file="users.db"):
        self.db_file = db_file
        self.create_tables()

    def create_tables(self):
        """Create necessary tables if they don't exist"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER,
            bot_id TEXT,
            username TEXT,
            full_name TEXT,
            language_code TEXT,
            first_seen TIMESTAMP,
            last_seen TIMESTAMP,
            total_downloads INTEGER DEFAULT 0,
            PRIMARY KEY (user_id, bot_id)
        )
        ''')
        
        # Create downloads table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS downloads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            bot_id TEXT,
            url TEXT,
            download_time TIMESTAMP,
            status TEXT,
            FOREIGN KEY (user_id, bot_id) REFERENCES users (user_id, bot_id)
        )
        ''')
        
        conn.commit()
        conn.close()

    def add_user(self, user_id: int, bot_id: str, username: str, full_name: str, language_code: str):
        """Add or update user information"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Check if user exists
        cursor.execute('SELECT user_id FROM users WHERE user_id = ? AND bot_id = ?', (user_id, bot_id))
        user = cursor.fetchone()
        
        if user is None:
            # Add new user
            cursor.execute('''
            INSERT INTO users (user_id, bot_id, username, full_name, language_code, first_seen, last_seen)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, bot_id, username, full_name, language_code, current_time, current_time))
        else:
            # Update existing user
            cursor.execute('''
            UPDATE users 
            SET username = ?, full_name = ?, language_code = ?, last_seen = ?
            WHERE user_id = ? AND bot_id = ?
            ''', (username, full_name, language_code, current_time, user_id, bot_id))
        
        conn.commit()
        conn.close()

    def add_download(self, user_id: int, bot_id: str, url: str, status: str):
        """Record a download attempt"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Add download record
        cursor.execute('''
        INSERT INTO downloads (user_id, bot_id, url, download_time, status)
        VALUES (?, ?, ?, ?, ?)
        ''', (user_id, bot_id, url, current_time, status))
        
        # Update user's total downloads
        cursor.execute('''
        UPDATE users 
        SET total_downloads = total_downloads + 1
        WHERE user_id = ? AND bot_id = ?
        ''', (user_id, bot_id))
        
        conn.commit()
        conn.close()

    def get_user_stats(self, user_id: int, bot_id: str):
        """Get user statistics"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT u.*, COUNT(d.id) as total_downloads
        FROM users u
        LEFT JOIN downloads d ON u.user_id = d.user_id AND u.bot_id = d.bot_id
        WHERE u.user_id = ? AND u.bot_id = ?
        GROUP BY u.user_id, u.bot_id
        ''', (user_id, bot_id))
        
        user = cursor.fetchone()
        conn.close()
        
        return user

    def get_all_users(self, bot_id: str = None):
        """Get all users with their download counts (optionally filtered by bot_id)"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        if bot_id:
            cursor.execute('''
            SELECT u.*, COUNT(d.id) as total_downloads
            FROM users u
            LEFT JOIN downloads d ON u.user_id = d.user_id AND u.bot_id = d.bot_id
            WHERE u.bot_id = ?
            GROUP BY u.user_id, u.bot_id
            ''', (bot_id,))
        else:
            cursor.execute('''
            SELECT u.*, COUNT(d.id) as total_downloads
            FROM users u
            LEFT JOIN downloads d ON u.user_id = d.user_id AND u.bot_id = d.bot_id
            GROUP BY u.user_id, u.bot_id
            ''')
        
        users = cursor.fetchall()
        conn.close()
        
        return users

    def get_top_users(self, limit: int = 5, bot_id: str = None):
        """Get top users by download count (optionally filtered by bot_id)"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        if bot_id:
            cursor.execute('''
            SELECT 
                u.user_id,
                u.username,
                u.full_name,
                COUNT(d.id) as total_downloads,
                u.first_seen
            FROM users u
            LEFT JOIN downloads d ON u.user_id = d.user_id AND u.bot_id = d.bot_id
            WHERE u.bot_id = ?
            GROUP BY u.user_id, u.bot_id
            ORDER BY total_downloads DESC
            LIMIT ?
            ''', (bot_id, limit))
        else:
            cursor.execute('''
            SELECT 
                u.user_id,
                u.username,
                u.full_name,
                COUNT(d.id) as total_downloads,
                u.first_seen
            FROM users u
            LEFT JOIN downloads d ON u.user_id = d.user_id AND u.bot_id = d.bot_id
            GROUP BY u.user_id, u.bot_id
            ORDER BY total_downloads DESC
            LIMIT ?
            ''', (limit,))
        
        users = cursor.fetchall()
        conn.close()
        
        return [
            {
                'user_id': user[0],
                'username': user[2],
                'full_name': user[3],
                'total_downloads': user[4],
                'first_seen': user[5]
            }
            for user in users
        ]

    def get_total_users(self, bot_id: str = None):
        """Get total number of users (optionally filtered by bot_id)"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        if bot_id:
            cursor.execute('SELECT COUNT(*) FROM users WHERE bot_id = ?', (bot_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM users')
        count = cursor.fetchone()[0]
        
        conn.close()
        return count

    def get_total_downloads(self, bot_id: str = None):
        """Get total number of downloads (optionally filtered by bot_id)"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        if bot_id:
            cursor.execute('SELECT COUNT(*) FROM downloads WHERE bot_id = ?', (bot_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM downloads')
        count = cursor.fetchone()[0]
        
        conn.close()
        return count

    def get_successful_downloads(self, bot_id: str = None):
        """Get number of successful downloads (optionally filtered by bot_id)"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        if bot_id:
            cursor.execute('SELECT COUNT(*) FROM downloads WHERE status = "success" AND bot_id = ?', (bot_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM downloads WHERE status = "success"')
        count = cursor.fetchone()[0]
        
        conn.close()
        return count

    def get_failed_downloads(self, bot_id: str = None):
        """Get number of failed downloads (optionally filtered by bot_id)"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        if bot_id:
            cursor.execute('SELECT COUNT(*) FROM downloads WHERE status != "success" AND bot_id = ?', (bot_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM downloads WHERE status != "success"')
        count = cursor.fetchone()[0]
        
        conn.close()
        return count 