import os
from database import Database

def migrate_database():
    """Migrate database to new schema with bot_id column"""
    db_file = "users.db"
    
    # Check if database exists
    if not os.path.exists(db_file):
        print("Database file not found. Creating new database with updated schema...")
        db = Database()
        print("✅ New database created successfully!")
        return
    
    print("🔄 Migrating existing database to new schema...")
    
    # Backup existing database
    backup_file = f"{db_file}.backup"
    if os.path.exists(backup_file):
        os.remove(backup_file)
    os.rename(db_file, backup_file)
    print(f"📦 Database backed up to {backup_file}")
    
    try:
        # Create new database with updated schema
        db = Database()
        print("✅ New database schema created successfully!")
        
        # Optionally, you can migrate data from backup here if needed
        # For now, we'll start fresh with the new schema
        
        print("🎉 Migration completed successfully!")
        print("📝 Note: Old data has been backed up. If you need to migrate existing data,")
        print("   you can modify this script to copy data from the backup file.")
        
    except Exception as e:
        # Restore backup if migration fails
        print(f"❌ Migration failed: {e}")
        print("🔄 Restoring backup...")
        if os.path.exists(db_file):
            os.remove(db_file)
        os.rename(backup_file, db_file)
        print("✅ Backup restored successfully!")
        raise

if __name__ == "__main__":
    migrate_database() 